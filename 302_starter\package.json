{"name": "302-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "commitlint": "commitlint", "commit": "git-cz"}, "dependencies": {"@302ai/ai-sdk": "^0.0.20", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@t3-oss/env-nextjs": "^0.11.1", "@tailwindcss/typography": "^0.5.15", "ahooks": "^3.8.4", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookies-next": "^4.3.0", "dexie": "^4.0.11", "embla-carousel-react": "^8.5.1", "file-saver": "^2.0.5", "immer": "^10.1.1", "input-otp": "^1.4.1", "iso-639-1": "^3.1.3", "jiti": "^2.4.1", "jotai": "^2.10.4", "ky": "^1.7.3", "lucide-react": "^0.453.0", "mitt": "^3.0.1", "nanoid": "^5.0.9", "next": "14.2.18", "next-intl": "^3.26.1", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "^9.4.4", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@commitlint/cz-commitlint": "^19.6.1", "@commitlint/prompt-cli": "^19.6.1", "@commitlint/types": "^19.5.0", "@types/file-saver": "^2.0.7", "@types/node": "^20.17.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "commitizen": "^4.3.1", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.7", "inquirer": "^9.3.7", "lint-staged": "^15.2.11", "postcss": "^8.4.49", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "^5.7.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --config .eslintrc.json --no-cache", "prettier --write"]}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903"}