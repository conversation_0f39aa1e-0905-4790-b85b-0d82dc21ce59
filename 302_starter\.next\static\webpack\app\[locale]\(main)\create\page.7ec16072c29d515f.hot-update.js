"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ConfigurationPanel = (param)=>{\n    let { onSmallSelectChange, onLargeSelectChange, onTextChange, smallSelectValue, largeSelectValue, textContent } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const [voiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__.voiceStoreAtom);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4 sm:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-none space-y-2 sm:w-[30%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: smallSelectValue,\n                            onValueChange: onSmallSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"small-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择语言\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"zh\",\n                                            children: \"中文\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"en\",\n                                            children: \"English\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"ja\",\n                                            children: \"日本語\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2 sm:w-[65%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: largeSelectValue,\n                            onValueChange: onLargeSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"large-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择声音模型\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"model1\",\n                                            children: \"标准女声\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"model2\",\n                                            children: \"标准男声\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"model3\",\n                                            children: \"甜美女声\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"model4\",\n                                            children: \"磁性男声\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                        id: \"text-content\",\n                        placeholder: \"请输入AI数字人需要说的内容...\",\n                        value: textContent,\n                        onChange: (e)=>onTextChange(e.target.value),\n                        className: \"min-h-[120px] resize-y focus:border-transparent focus:ring-2 focus:ring-primary\",\n                        rows: 5\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConfigurationPanel, \"9WOyjciEqfcrB6BNgulNUNXvYQg=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom\n    ];\n});\n_c = ConfigurationPanel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ConfigurationPanel);\nvar _c;\n$RefreshReg$(_c, \"ConfigurationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx\n"));

/***/ })

});