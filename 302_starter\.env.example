# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# ============================================
# If you want deploy out of 302 Team's server, set this to your own API key which you can generate on 302 dashboard
# If you don't know what this is, keep it empty
NEXT_PUBLIC_302_API_KEY=
# 302AI_FETCH_API_URL is the URL of the 302AI Fetch API
NEXT_PUBLIC_API_URL=https://api.302.ai
# Default model name is gpt-4o-mini, if you want to change it, you can set it here
NEXT_PUBLIC_DEFAULT_MODEL_NAME=gpt-4o-mini
# If you want to hide our brand, set this to true
NEXT_PUBLIC_HIDE_BRAND=true


# ============================================
# Log level: trace, debug, info, warn, error
# Both client and server can access this variable
NEXT_PUBLIC_LOG_LEVEL=debug
# 302AI_AUTH_API_URL is the URL of the 302AI Auth API
NEXT_PUBLIC_AUTH_API_URL=https://dash-api.302.ai
# Our website in China
NEXT_PUBLIC_302_WEBSITE_URL_CHINA=https://302ai.cn/
# Our website globally
NEXT_PUBLIC_302_WEBSITE_URL_GLOBAL=https://302.ai/
# Default is auth page is /auth, if you want to change it, you can set it here
NEXT_PUBLIC_AUTH_PATH=/auth
# If you want to deploy on your own server, keep it empty
NEXT_PUBLIC_DEV_HOST_NAME=
# If you are deploying in China, set this to true
NEXT_PUBLIC_IS_CHINA=false
# Default locale is en, if you want to change it, you can set it here
NEXT_PUBLIC_DEFAULT_LOCALE=en
