"use client"
import React, { useState } from 'react'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Card } from '@/components/ui/card'
import { VideoPreviewFrameProps } from '../types'

const VideoPreviewFrame: React.FC<VideoPreviewFrameProps> = ({
  coverImage,
  aspectRatio = 16 / 9,
  placeholder = '视频预览'
}) => {
  const [imageError, setImageError] = useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  return (
    <Card className="w-full overflow-hidden">
      <AspectRatio ratio={aspectRatio}>
        <div className="w-full h-full bg-muted border-2 border-dashed border-border flex items-center justify-center rounded-md">
          {coverImage && !imageError ? (
            <Image
              src={coverImage}
              alt="Video preview"
              fill
              className="object-cover rounded-md"
              onError={handleImageError}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 40vw, 33vw"
            />
          ) : (
            <div className="flex flex-col items-center justify-center space-y-2">
              <div className="w-16 h-16 bg-muted-foreground/20 rounded-lg flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-muted-foreground"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <p className="text-muted-foreground text-sm text-center">{placeholder}</p>
            </div>
          )}
        </div>
      </AspectRatio>
    </Card>
  )
}

export default VideoPreviewFrame
