"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(main)/create/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx":
/*!**************************************************************************!*\
  !*** ./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/.pnpm/next-intl@3.26.1_next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1__react@18.3.1/node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! jotai */ \"(app-pages-browser)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var _stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/slices/voice_store */ \"(app-pages-browser)/./src/stores/slices/voice_store.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ConfigurationPanel = (param)=>{\n    let { onSmallSelectChange, onLargeSelectChange, onTextChange, smallSelectValue, largeSelectValue, textContent } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const [voiceStore] = (0,jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom)(_stores_slices_voice_store__WEBPACK_IMPORTED_MODULE_4__.voiceStoreAtom);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-4 sm:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-none space-y-2 sm:w-[30%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: smallSelectValue,\n                            onValueChange: onSmallSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"small-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择语言\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"zh\",\n                                            children: \"中文\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"en\",\n                                            children: \"English\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                            value: \"ja\",\n                                            children: \"日本語\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2 sm:w-[65%]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                            value: largeSelectValue,\n                            onValueChange: onLargeSelectChange,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                    id: \"large-select\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                        placeholder: \"选择声音模型\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                    children: voiceStore.voiceList.map((voiceGroup)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1.5 text-sm font-semibold text-muted-foreground\",\n                                                    children: voiceGroup.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                voiceGroup.children.map((voice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                        value: \"\".concat(voiceGroup.value, \":\").concat(voice.value),\n                                                        children: voice.label\n                                                    }, voice.key, false, {\n                                                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, voiceGroup.key, true, {\n                                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                        id: \"text-content\",\n                        placeholder: \"请输入AI数字人需要说的内容...\",\n                        value: textContent,\n                        onChange: (e)=>onTextChange(e.target.value),\n                        className: \"min-h-[120px] resize-y focus:border-transparent focus:ring-2 focus:ring-primary\",\n                        rows: 5\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code\\\\数字人\\\\302_starter\\\\src\\\\app\\\\[locale]\\\\(main)\\\\create\\\\components\\\\ConfigurationPanel.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ConfigurationPanel, \"9WOyjciEqfcrB6BNgulNUNXvYQg=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        jotai__WEBPACK_IMPORTED_MODULE_6__.useAtom\n    ];\n});\n_c = ConfigurationPanel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ConfigurationPanel);\nvar _c;\n$RefreshReg$(_c, \"ConfigurationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(main)/create/components/ConfigurationPanel.tsx\n"));

/***/ })

});