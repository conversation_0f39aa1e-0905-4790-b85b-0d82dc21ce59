"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ky@1.7.3";
exports.ids = ["vendor-chunks/ky@1.7.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/Ky.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/Ky.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ky: () => (/* binding */ Ky)\n/* harmony export */ });\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/HTTPError.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/merge.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/normalize.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/normalize.js\");\n/* harmony import */ var _utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/timeout.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/timeout.js\");\n/* harmony import */ var _utils_delay_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/delay.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/delay.js\");\n/* harmony import */ var _utils_options_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/options.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/options.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js\");\n\n\n\n\n\n\n\n\nclass Ky {\n    static create(input, options) {\n        const ky = new Ky(input, options);\n        const function_ = async () => {\n            if (typeof ky._options.timeout === 'number' && ky._options.timeout > _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout) {\n                throw new RangeError(`The \\`timeout\\` option cannot be greater than ${_constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout}`);\n            }\n            // Delay the fetch so that body method shortcuts can set the Accept header\n            await Promise.resolve();\n            let response = await ky._fetch();\n            for (const hook of ky._options.hooks.afterResponse) {\n                // eslint-disable-next-line no-await-in-loop\n                const modifiedResponse = await hook(ky.request, ky._options, ky._decorateResponse(response.clone()));\n                if (modifiedResponse instanceof globalThis.Response) {\n                    response = modifiedResponse;\n                }\n            }\n            ky._decorateResponse(response);\n            if (!response.ok && ky._options.throwHttpErrors) {\n                let error = new _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError(response, ky.request, ky._options);\n                for (const hook of ky._options.hooks.beforeError) {\n                    // eslint-disable-next-line no-await-in-loop\n                    error = await hook(error);\n                }\n                throw error;\n            }\n            // If `onDownloadProgress` is passed, it uses the stream API internally\n            /* istanbul ignore next */\n            if (ky._options.onDownloadProgress) {\n                if (typeof ky._options.onDownloadProgress !== 'function') {\n                    throw new TypeError('The `onDownloadProgress` option must be a function');\n                }\n                if (!_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsResponseStreams) {\n                    throw new Error('Streams are not supported in your environment. `ReadableStream` is missing.');\n                }\n                return ky._stream(response.clone(), ky._options.onDownloadProgress);\n            }\n            return response;\n        };\n        const isRetriableMethod = ky._options.retry.methods.includes(ky.request.method.toLowerCase());\n        const result = (isRetriableMethod ? ky._retry(function_) : function_());\n        for (const [type, mimeType] of Object.entries(_constants_js__WEBPACK_IMPORTED_MODULE_0__.responseTypes)) {\n            result[type] = async () => {\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                ky.request.headers.set('accept', ky.request.headers.get('accept') || mimeType);\n                const response = await result;\n                if (type === 'json') {\n                    if (response.status === 204) {\n                        return '';\n                    }\n                    const arrayBuffer = await response.clone().arrayBuffer();\n                    const responseSize = arrayBuffer.byteLength;\n                    if (responseSize === 0) {\n                        return '';\n                    }\n                    if (options.parseJson) {\n                        return options.parseJson(await response.text());\n                    }\n                }\n                return response[type]();\n            };\n        }\n        return result;\n    }\n    request;\n    abortController;\n    _retryCount = 0;\n    _input;\n    _options;\n    // eslint-disable-next-line complexity\n    constructor(input, options = {}) {\n        this._input = input;\n        this._options = {\n            ...options,\n            headers: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.mergeHeaders)(this._input.headers, options.headers),\n            hooks: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.mergeHooks)({\n                beforeRequest: [],\n                beforeRetry: [],\n                beforeError: [],\n                afterResponse: [],\n            }, options.hooks),\n            method: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRequestMethod)(options.method ?? this._input.method),\n            // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n            prefixUrl: String(options.prefixUrl || ''),\n            retry: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRetryOptions)(options.retry),\n            throwHttpErrors: options.throwHttpErrors !== false,\n            timeout: options.timeout ?? 10_000,\n            fetch: options.fetch ?? globalThis.fetch.bind(globalThis),\n        };\n        if (typeof this._input !== 'string' && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {\n            throw new TypeError('`input` must be a string, URL, or Request');\n        }\n        if (this._options.prefixUrl && typeof this._input === 'string') {\n            if (this._input.startsWith('/')) {\n                throw new Error('`input` must not begin with a slash when using `prefixUrl`');\n            }\n            if (!this._options.prefixUrl.endsWith('/')) {\n                this._options.prefixUrl += '/';\n            }\n            this._input = this._options.prefixUrl + this._input;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsAbortController) {\n            this.abortController = new globalThis.AbortController();\n            const originalSignal = this._options.signal ?? this._input.signal;\n            originalSignal?.addEventListener('abort', () => {\n                this.abortController.abort(originalSignal.reason);\n            });\n            this._options.signal = this.abortController.signal;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsRequestStreams) {\n            // @ts-expect-error - Types are outdated.\n            this._options.duplex = 'half';\n        }\n        if (this._options.json !== undefined) {\n            this._options.body = this._options.stringifyJson?.(this._options.json) ?? JSON.stringify(this._options.json);\n            this._options.headers.set('content-type', this._options.headers.get('content-type') ?? 'application/json');\n        }\n        this.request = new globalThis.Request(this._input, this._options);\n        if (this._options.searchParams) {\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const textSearchParams = typeof this._options.searchParams === 'string'\n                ? this._options.searchParams.replace(/^\\?/, '')\n                : new URLSearchParams(this._options.searchParams).toString();\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const searchParams = '?' + textSearchParams;\n            const url = this.request.url.replace(/(?:\\?.*?)?(?=#|$)/, searchParams);\n            // To provide correct form boundary, Content-Type header should be deleted each time when new Request instantiated from another one\n            if (((_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsFormData && this._options.body instanceof globalThis.FormData)\n                || this._options.body instanceof URLSearchParams) && !(this._options.headers && this._options.headers['content-type'])) {\n                this.request.headers.delete('content-type');\n            }\n            // The spread of `this.request` is required as otherwise it misses the `duplex` option for some reason and throws.\n            this.request = new globalThis.Request(new globalThis.Request(url, { ...this.request }), this._options);\n        }\n    }\n    _calculateRetryDelay(error) {\n        this._retryCount++;\n        if (this._retryCount > this._options.retry.limit || error instanceof _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError) {\n            throw error;\n        }\n        if (error instanceof _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError) {\n            if (!this._options.retry.statusCodes.includes(error.response.status)) {\n                throw error;\n            }\n            const retryAfter = error.response.headers.get('Retry-After')\n                ?? error.response.headers.get('RateLimit-Reset')\n                ?? error.response.headers.get('X-RateLimit-Reset') // GitHub\n                ?? error.response.headers.get('X-Rate-Limit-Reset'); // Twitter\n            if (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {\n                let after = Number(retryAfter) * 1000;\n                if (Number.isNaN(after)) {\n                    after = Date.parse(retryAfter) - Date.now();\n                }\n                else if (after >= Date.parse('2024-01-01')) {\n                    // A large number is treated as a timestamp (fixed threshold protects against clock skew)\n                    after -= Date.now();\n                }\n                const max = this._options.retry.maxRetryAfter ?? after;\n                return after < max ? after : max;\n            }\n            if (error.response.status === 413) {\n                throw error;\n            }\n        }\n        const retryDelay = this._options.retry.delay(this._retryCount);\n        return Math.min(this._options.retry.backoffLimit, retryDelay);\n    }\n    _decorateResponse(response) {\n        if (this._options.parseJson) {\n            response.json = async () => this._options.parseJson(await response.text());\n        }\n        return response;\n    }\n    async _retry(function_) {\n        try {\n            return await function_();\n        }\n        catch (error) {\n            const ms = Math.min(this._calculateRetryDelay(error), _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout);\n            if (this._retryCount < 1) {\n                throw error;\n            }\n            await (0,_utils_delay_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ms, { signal: this._options.signal });\n            for (const hook of this._options.hooks.beforeRetry) {\n                // eslint-disable-next-line no-await-in-loop\n                const hookResult = await hook({\n                    request: this.request,\n                    options: this._options,\n                    error: error,\n                    retryCount: this._retryCount,\n                });\n                // If `stop` is returned from the hook, the retry process is stopped\n                if (hookResult === _constants_js__WEBPACK_IMPORTED_MODULE_0__.stop) {\n                    return;\n                }\n            }\n            return this._retry(function_);\n        }\n    }\n    async _fetch() {\n        for (const hook of this._options.hooks.beforeRequest) {\n            // eslint-disable-next-line no-await-in-loop\n            const result = await hook(this.request, this._options);\n            if (result instanceof Request) {\n                this.request = result;\n                break;\n            }\n            if (result instanceof Response) {\n                return result;\n            }\n        }\n        const nonRequestOptions = (0,_utils_options_js__WEBPACK_IMPORTED_MODULE_6__.findUnknownOptions)(this.request, this._options);\n        // Cloning is done here to prepare in advance for retries\n        const mainRequest = this.request;\n        this.request = mainRequest.clone();\n        if (this._options.timeout === false) {\n            return this._options.fetch(mainRequest, nonRequestOptions);\n        }\n        return (0,_utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(mainRequest, nonRequestOptions, this.abortController, this._options);\n    }\n    /* istanbul ignore next */\n    _stream(response, onDownloadProgress) {\n        const totalBytes = Number(response.headers.get('content-length')) || 0;\n        let transferredBytes = 0;\n        if (response.status === 204) {\n            if (onDownloadProgress) {\n                onDownloadProgress({ percent: 1, totalBytes, transferredBytes }, new Uint8Array());\n            }\n            return new globalThis.Response(null, {\n                status: response.status,\n                statusText: response.statusText,\n                headers: response.headers,\n            });\n        }\n        return new globalThis.Response(new globalThis.ReadableStream({\n            async start(controller) {\n                const reader = response.body.getReader();\n                if (onDownloadProgress) {\n                    onDownloadProgress({ percent: 0, transferredBytes: 0, totalBytes }, new Uint8Array());\n                }\n                async function read() {\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        return;\n                    }\n                    if (onDownloadProgress) {\n                        transferredBytes += value.byteLength;\n                        const percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n                        onDownloadProgress({ percent, transferredBytes, totalBytes }, value);\n                    }\n                    controller.enqueue(value);\n                    await read();\n                }\n                await read();\n            },\n        }), {\n            status: response.status,\n            statusText: response.statusText,\n            headers: response.headers,\n        });\n    }\n}\n//# sourceMappingURL=Ky.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/Ky.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kyOptionKeys: () => (/* binding */ kyOptionKeys),\n/* harmony export */   maxSafeTimeout: () => (/* binding */ maxSafeTimeout),\n/* harmony export */   requestMethods: () => (/* binding */ requestMethods),\n/* harmony export */   requestOptionsRegistry: () => (/* binding */ requestOptionsRegistry),\n/* harmony export */   responseTypes: () => (/* binding */ responseTypes),\n/* harmony export */   stop: () => (/* binding */ stop),\n/* harmony export */   supportsAbortController: () => (/* binding */ supportsAbortController),\n/* harmony export */   supportsFormData: () => (/* binding */ supportsFormData),\n/* harmony export */   supportsRequestStreams: () => (/* binding */ supportsRequestStreams),\n/* harmony export */   supportsResponseStreams: () => (/* binding */ supportsResponseStreams)\n/* harmony export */ });\nconst supportsRequestStreams = (() => {\n    let duplexAccessed = false;\n    let hasContentType = false;\n    const supportsReadableStream = typeof globalThis.ReadableStream === 'function';\n    const supportsRequest = typeof globalThis.Request === 'function';\n    if (supportsReadableStream && supportsRequest) {\n        try {\n            hasContentType = new globalThis.Request('https://empty.invalid', {\n                body: new globalThis.ReadableStream(),\n                method: 'POST',\n                // @ts-expect-error - Types are outdated.\n                get duplex() {\n                    duplexAccessed = true;\n                    return 'half';\n                },\n            }).headers.has('Content-Type');\n        }\n        catch (error) {\n            // QQBrowser on iOS throws \"unsupported BodyInit type\" error (see issue #581)\n            if (error instanceof Error && error.message === 'unsupported BodyInit type') {\n                return false;\n            }\n            throw error;\n        }\n    }\n    return duplexAccessed && !hasContentType;\n})();\nconst supportsAbortController = typeof globalThis.AbortController === 'function';\nconst supportsResponseStreams = typeof globalThis.ReadableStream === 'function';\nconst supportsFormData = typeof globalThis.FormData === 'function';\nconst requestMethods = ['get', 'post', 'put', 'patch', 'head', 'delete'];\nconst validate = () => undefined;\nvalidate();\nconst responseTypes = {\n    json: 'application/json',\n    text: 'text/*',\n    formData: 'multipart/form-data',\n    arrayBuffer: '*/*',\n    blob: '*/*',\n};\n// The maximum value of a 32bit int (see issue #117)\nconst maxSafeTimeout = 2_147_483_647;\nconst stop = Symbol('stop');\nconst kyOptionKeys = {\n    json: true,\n    parseJson: true,\n    stringifyJson: true,\n    searchParams: true,\n    prefixUrl: true,\n    retry: true,\n    timeout: true,\n    hooks: true,\n    throwHttpErrors: true,\n    onDownloadProgress: true,\n    fetch: true,\n};\nconst requestOptionsRegistry = {\n    method: true,\n    headers: true,\n    body: true,\n    mode: true,\n    credentials: true,\n    cache: true,\n    redirect: true,\n    referrer: true,\n    referrerPolicy: true,\n    integrity: true,\n    keepalive: true,\n    signal: true,\n    window: true,\n    dispatcher: true,\n    duplex: true,\n    priority: true,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/HTTPError.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/HTTPError.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* binding */ HTTPError)\n/* harmony export */ });\nclass HTTPError extends Error {\n    response;\n    request;\n    options;\n    constructor(response, request, options) {\n        const code = (response.status || response.status === 0) ? response.status : '';\n        const title = response.statusText || '';\n        const status = `${code} ${title}`.trim();\n        const reason = status ? `status code ${status}` : 'an unknown error';\n        super(`Request failed with ${reason}: ${request.method} ${request.url}`);\n        this.name = 'HTTPError';\n        this.response = response;\n        this.request = request;\n        this.options = options;\n    }\n}\n//# sourceMappingURL=HTTPError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvSFRUUEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixNQUFNLEVBQUUsTUFBTTtBQUN4QywrQ0FBK0MsT0FBTztBQUN0RCxxQ0FBcUMsT0FBTyxJQUFJLGdCQUFnQixFQUFFLFlBQVk7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8zMDItc3RhcnRlci8uL25vZGVfbW9kdWxlcy8ucG5wbS9reUAxLjcuMy9ub2RlX21vZHVsZXMva3kvZGlzdHJpYnV0aW9uL2Vycm9ycy9IVFRQRXJyb3IuanM/YTg3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgSFRUUEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIHJlc3BvbnNlO1xuICAgIHJlcXVlc3Q7XG4gICAgb3B0aW9ucztcbiAgICBjb25zdHJ1Y3RvcihyZXNwb25zZSwgcmVxdWVzdCwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBjb2RlID0gKHJlc3BvbnNlLnN0YXR1cyB8fCByZXNwb25zZS5zdGF0dXMgPT09IDApID8gcmVzcG9uc2Uuc3RhdHVzIDogJyc7XG4gICAgICAgIGNvbnN0IHRpdGxlID0gcmVzcG9uc2Uuc3RhdHVzVGV4dCB8fCAnJztcbiAgICAgICAgY29uc3Qgc3RhdHVzID0gYCR7Y29kZX0gJHt0aXRsZX1gLnRyaW0oKTtcbiAgICAgICAgY29uc3QgcmVhc29uID0gc3RhdHVzID8gYHN0YXR1cyBjb2RlICR7c3RhdHVzfWAgOiAnYW4gdW5rbm93biBlcnJvcic7XG4gICAgICAgIHN1cGVyKGBSZXF1ZXN0IGZhaWxlZCB3aXRoICR7cmVhc29ufTogJHtyZXF1ZXN0Lm1ldGhvZH0gJHtyZXF1ZXN0LnVybH1gKTtcbiAgICAgICAgdGhpcy5uYW1lID0gJ0hUVFBFcnJvcic7XG4gICAgICAgIHRoaXMucmVzcG9uc2UgPSByZXNwb25zZTtcbiAgICAgICAgdGhpcy5yZXF1ZXN0ID0gcmVxdWVzdDtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucztcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1IVFRQRXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/HTTPError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError)\n/* harmony export */ });\nclass TimeoutError extends Error {\n    request;\n    constructor(request) {\n        super(`Request timed out: ${request.method} ${request.url}`);\n        this.name = 'TimeoutError';\n        this.request = request;\n    }\n}\n//# sourceMappingURL=TimeoutError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSxvQ0FBb0MsZ0JBQWdCLEVBQUUsWUFBWTtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vMzAyLXN0YXJ0ZXIvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzPzE3YzkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIFRpbWVvdXRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICByZXF1ZXN0O1xuICAgIGNvbnN0cnVjdG9yKHJlcXVlc3QpIHtcbiAgICAgICAgc3VwZXIoYFJlcXVlc3QgdGltZWQgb3V0OiAke3JlcXVlc3QubWV0aG9kfSAke3JlcXVlc3QudXJsfWApO1xuICAgICAgICB0aGlzLm5hbWUgPSAnVGltZW91dEVycm9yJztcbiAgICAgICAgdGhpcy5yZXF1ZXN0ID0gcmVxdWVzdDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1UaW1lb3V0RXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* reexport safe */ _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__.HTTPError),\n/* harmony export */   TimeoutError: () => (/* reexport safe */ _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/Ky.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/Ky.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./core/constants.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/merge.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/HTTPError.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errors/TimeoutError.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js\");\n/*! MIT License © Sindre Sorhus */\n\n\n\nconst createInstance = (defaults) => {\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    const ky = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options));\n    for (const method of _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.requestMethods) {\n        // eslint-disable-next-line @typescript-eslint/promise-function-async\n        ky[method] = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options, { method }));\n    }\n    ky.create = (newDefaults) => createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(newDefaults));\n    ky.extend = (newDefaults) => {\n        if (typeof newDefaults === 'function') {\n            newDefaults = newDefaults(defaults ?? {});\n        }\n        return createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, newDefaults));\n    };\n    ky.stop = _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.stop;\n    return ky;\n};\nconst ky = createInstance();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ky);\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/delay.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/delay.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ delay)\n/* harmony export */ });\n// https://github.com/sindresorhus/delay/tree/ab98ae8dfcb38e1593286c94d934e70d14a4e111\nasync function delay(ms, { signal }) {\n    return new Promise((resolve, reject) => {\n        if (signal) {\n            signal.throwIfAborted();\n            signal.addEventListener('abort', abortHandler, { once: true });\n        }\n        function abortHandler() {\n            clearTimeout(timeoutId);\n            reject(signal.reason);\n        }\n        const timeoutId = setTimeout(() => {\n            signal?.removeEventListener('abort', abortHandler);\n            resolve();\n        }, ms);\n    });\n}\n//# sourceMappingURL=delay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9kZWxheS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDZSwyQkFBMkIsUUFBUTtBQUNsRDtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsWUFBWTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vMzAyLXN0YXJ0ZXIvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9kZWxheS5qcz8wYTdmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9zaW5kcmVzb3JodXMvZGVsYXkvdHJlZS9hYjk4YWU4ZGZjYjM4ZTE1OTMyODZjOTRkOTM0ZTcwZDE0YTRlMTExXG5leHBvcnQgZGVmYXVsdCBhc3luYyBmdW5jdGlvbiBkZWxheShtcywgeyBzaWduYWwgfSkge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIGlmIChzaWduYWwpIHtcbiAgICAgICAgICAgIHNpZ25hbC50aHJvd0lmQWJvcnRlZCgpO1xuICAgICAgICAgICAgc2lnbmFsLmFkZEV2ZW50TGlzdGVuZXIoJ2Fib3J0JywgYWJvcnRIYW5kbGVyLCB7IG9uY2U6IHRydWUgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZnVuY3Rpb24gYWJvcnRIYW5kbGVyKCkge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG4gICAgICAgICAgICByZWplY3Qoc2lnbmFsLnJlYXNvbik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBzaWduYWw/LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Fib3J0JywgYWJvcnRIYW5kbGVyKTtcbiAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgfSwgbXMpO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVsYXkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/delay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/is.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/is.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObject: () => (/* binding */ isObject)\n/* harmony export */ });\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst isObject = (value) => value !== null && typeof value === 'object';\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9pcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vMzAyLXN0YXJ0ZXIvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9pcy5qcz82ZmRmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvYmFuLXR5cGVzXG5leHBvcnQgY29uc3QgaXNPYmplY3QgPSAodmFsdWUpID0+IHZhbHVlICE9PSBudWxsICYmIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/is.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/merge.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/merge.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   mergeHeaders: () => (/* binding */ mergeHeaders),\n/* harmony export */   mergeHooks: () => (/* binding */ mergeHooks),\n/* harmony export */   validateAndMerge: () => (/* binding */ validateAndMerge)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/is.js\");\n\nconst validateAndMerge = (...sources) => {\n    for (const source of sources) {\n        if ((!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source) || Array.isArray(source)) && source !== undefined) {\n            throw new TypeError('The `options` argument must be an object');\n        }\n    }\n    return deepMerge({}, ...sources);\n};\nconst mergeHeaders = (source1 = {}, source2 = {}) => {\n    const result = new globalThis.Headers(source1);\n    const isHeadersInstance = source2 instanceof globalThis.Headers;\n    const source = new globalThis.Headers(source2);\n    for (const [key, value] of source.entries()) {\n        if ((isHeadersInstance && value === 'undefined') || value === undefined) {\n            result.delete(key);\n        }\n        else {\n            result.set(key, value);\n        }\n    }\n    return result;\n};\nfunction newHookValue(original, incoming, property) {\n    return (Object.hasOwn(incoming, property) && incoming[property] === undefined)\n        ? []\n        : deepMerge(original[property] ?? [], incoming[property] ?? []);\n}\nconst mergeHooks = (original = {}, incoming = {}) => ({\n    beforeRequest: newHookValue(original, incoming, 'beforeRequest'),\n    beforeRetry: newHookValue(original, incoming, 'beforeRetry'),\n    afterResponse: newHookValue(original, incoming, 'afterResponse'),\n    beforeError: newHookValue(original, incoming, 'beforeError'),\n});\n// TODO: Make this strongly-typed (no `any`).\nconst deepMerge = (...sources) => {\n    let returnValue = {};\n    let headers = {};\n    let hooks = {};\n    for (const source of sources) {\n        if (Array.isArray(source)) {\n            if (!Array.isArray(returnValue)) {\n                returnValue = [];\n            }\n            returnValue = [...returnValue, ...source];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source)) {\n            for (let [key, value] of Object.entries(source)) {\n                if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(value) && key in returnValue) {\n                    value = deepMerge(returnValue[key], value);\n                }\n                returnValue = { ...returnValue, [key]: value };\n            }\n            if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source.hooks)) {\n                hooks = mergeHooks(hooks, source.hooks);\n                returnValue.hooks = hooks;\n            }\n            if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source.headers)) {\n                headers = mergeHeaders(headers, source.headers);\n                returnValue.headers = headers;\n            }\n        }\n    }\n    return returnValue;\n};\n//# sourceMappingURL=merge.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/normalize.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/normalize.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeRequestMethod: () => (/* binding */ normalizeRequestMethod),\n/* harmony export */   normalizeRetryOptions: () => (/* binding */ normalizeRetryOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js\");\n\nconst normalizeRequestMethod = (input) => _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestMethods.includes(input) ? input.toUpperCase() : input;\nconst retryMethods = ['get', 'put', 'head', 'delete', 'options', 'trace'];\nconst retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];\nconst retryAfterStatusCodes = [413, 429, 503];\nconst defaultRetryOptions = {\n    limit: 2,\n    methods: retryMethods,\n    statusCodes: retryStatusCodes,\n    afterStatusCodes: retryAfterStatusCodes,\n    maxRetryAfter: Number.POSITIVE_INFINITY,\n    backoffLimit: Number.POSITIVE_INFINITY,\n    delay: attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000,\n};\nconst normalizeRetryOptions = (retry = {}) => {\n    if (typeof retry === 'number') {\n        return {\n            ...defaultRetryOptions,\n            limit: retry,\n        };\n    }\n    if (retry.methods && !Array.isArray(retry.methods)) {\n        throw new Error('retry.methods must be an array');\n    }\n    if (retry.statusCodes && !Array.isArray(retry.statusCodes)) {\n        throw new Error('retry.statusCodes must be an array');\n    }\n    return {\n        ...defaultRetryOptions,\n        ...retry,\n    };\n};\n//# sourceMappingURL=normalize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/options.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/options.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findUnknownOptions: () => (/* binding */ findUnknownOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/core/constants.js\");\n\nconst findUnknownOptions = (request, options) => {\n    const unknownOptions = {};\n    for (const key in options) {\n        if (!(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestOptionsRegistry) && !(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.kyOptionKeys) && !(key in request)) {\n            unknownOptions[key] = options[key];\n        }\n    }\n    return unknownOptions;\n};\n//# sourceMappingURL=options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRFO0FBQ3JFO0FBQ1A7QUFDQTtBQUNBLHFCQUFxQixzRUFBc0IsY0FBYyw0REFBWTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8zMDItc3RhcnRlci8uL25vZGVfbW9kdWxlcy8ucG5wbS9reUAxLjcuMy9ub2RlX21vZHVsZXMva3kvZGlzdHJpYnV0aW9uL3V0aWxzL29wdGlvbnMuanM/NjA0ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBreU9wdGlvbktleXMsIHJlcXVlc3RPcHRpb25zUmVnaXN0cnkgfSBmcm9tICcuLi9jb3JlL2NvbnN0YW50cy5qcyc7XG5leHBvcnQgY29uc3QgZmluZFVua25vd25PcHRpb25zID0gKHJlcXVlc3QsIG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB1bmtub3duT3B0aW9ucyA9IHt9O1xuICAgIGZvciAoY29uc3Qga2V5IGluIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKCEoa2V5IGluIHJlcXVlc3RPcHRpb25zUmVnaXN0cnkpICYmICEoa2V5IGluIGt5T3B0aW9uS2V5cykgJiYgIShrZXkgaW4gcmVxdWVzdCkpIHtcbiAgICAgICAgICAgIHVua25vd25PcHRpb25zW2tleV0gPSBvcHRpb25zW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHVua25vd25PcHRpb25zO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW9wdGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/timeout.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/timeout.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ timeout)\n/* harmony export */ });\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/errors/TimeoutError.js\");\n\n// `Promise.race()` workaround (#91)\nasync function timeout(request, init, abortController, options) {\n    return new Promise((resolve, reject) => {\n        const timeoutId = setTimeout(() => {\n            if (abortController) {\n                abortController.abort();\n            }\n            reject(new _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__.TimeoutError(request));\n        }, options.timeout);\n        void options\n            .fetch(request, init)\n            .then(resolve)\n            .catch(reject)\n            .then(() => {\n            clearTimeout(timeoutId);\n        });\n    });\n}\n//# sourceMappingURL=timeout.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0va3lAMS43LjMvbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy90aW1lb3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ3pEO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFZO0FBQ25DLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8zMDItc3RhcnRlci8uL25vZGVfbW9kdWxlcy8ucG5wbS9reUAxLjcuMy9ub2RlX21vZHVsZXMva3kvZGlzdHJpYnV0aW9uL3V0aWxzL3RpbWVvdXQuanM/MGJmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUaW1lb3V0RXJyb3IgfSBmcm9tICcuLi9lcnJvcnMvVGltZW91dEVycm9yLmpzJztcbi8vIGBQcm9taXNlLnJhY2UoKWAgd29ya2Fyb3VuZCAoIzkxKVxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gdGltZW91dChyZXF1ZXN0LCBpbml0LCBhYm9ydENvbnRyb2xsZXIsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGlmIChhYm9ydENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICBhYm9ydENvbnRyb2xsZXIuYWJvcnQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlamVjdChuZXcgVGltZW91dEVycm9yKHJlcXVlc3QpKTtcbiAgICAgICAgfSwgb3B0aW9ucy50aW1lb3V0KTtcbiAgICAgICAgdm9pZCBvcHRpb25zXG4gICAgICAgICAgICAuZmV0Y2gocmVxdWVzdCwgaW5pdClcbiAgICAgICAgICAgIC50aGVuKHJlc29sdmUpXG4gICAgICAgICAgICAuY2F0Y2gocmVqZWN0KVxuICAgICAgICAgICAgLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG4gICAgICAgIH0pO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGltZW91dC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/ky@1.7.3/node_modules/ky/distribution/utils/timeout.js\n");

/***/ })

};
;