import React from 'react'
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from './app-sidebar'

const MainLayout = ({children}:{children: React.ReactNode}) => {
  return (
    <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <div className="flex flex-1 flex-col gap-4 p-4  min-h-screen">
        <div className="">
        {children}
        </div>
      </div>
    </SidebarInset>
  </SidebarProvider>
  )
}

export default MainLayout