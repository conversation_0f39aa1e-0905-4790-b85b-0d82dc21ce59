"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai@2.10.4_@types+react@18.3.17_react@18.3.1";
exports.ids = ["vendor-chunks/jotai@2.10.4_@types+react@18.3.17_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useAtom: () => (/* binding */ useAtom),\n/* harmony export */   useAtomValue: () => (/* binding */ useAtomValue),\n/* harmony export */   useSetAtom: () => (/* binding */ useSetAtom),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.18_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla.mjs\");\n/* __next_internal_client_entry_do_not_use__ Provider,useAtom,useAtomValue,useSetAtom,useStore auto */ \n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nconst useStore = (options)=>{\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(StoreContext);\n    return (options == null ? void 0 : options.store) || store || (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.getDefaultStore)();\n};\nconst Provider = ({ children, store })=>{\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n    if (!store && !storeRef.current) {\n        storeRef.current = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)();\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(StoreContext.Provider, {\n        value: store || storeRef.current\n    }, children);\n};\nconst isPromiseLike = (x)=>typeof (x == null ? void 0 : x.then) === \"function\";\nconst attachPromiseMeta = (promise)=>{\n    promise.status = \"pending\";\n    promise.then((v)=>{\n        promise.status = \"fulfilled\";\n        promise.value = v;\n    }, (e)=>{\n        promise.status = \"rejected\";\n        promise.reason = e;\n    });\n};\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || ((promise)=>{\n    if (promise.status === \"pending\") {\n        throw promise;\n    } else if (promise.status === \"fulfilled\") {\n        return promise.value;\n    } else if (promise.status === \"rejected\") {\n        throw promise.reason;\n    } else {\n        attachPromiseMeta(promise);\n        throw promise;\n    }\n});\nconst continuablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst createContinuablePromise = (promise)=>{\n    let continuablePromise = continuablePromiseMap.get(promise);\n    if (!continuablePromise) {\n        continuablePromise = new Promise((resolve, reject)=>{\n            let curr = promise;\n            const onFulfilled = (me)=>(v)=>{\n                    if (curr === me) {\n                        resolve(v);\n                    }\n                };\n            const onRejected = (me)=>(e)=>{\n                    if (curr === me) {\n                        reject(e);\n                    }\n                };\n            const registerCancelHandler = (p)=>{\n                if (\"onCancel\" in p && typeof p.onCancel === \"function\") {\n                    p.onCancel((nextValue)=>{\n                        if (( false ? 0 : void 0) !== \"production\" && nextValue === p) {\n                            throw new Error(\"[Bug] p is not updated even after cancelation\");\n                        }\n                        if (isPromiseLike(nextValue)) {\n                            continuablePromiseMap.set(nextValue, continuablePromise);\n                            curr = nextValue;\n                            nextValue.then(onFulfilled(nextValue), onRejected(nextValue));\n                            registerCancelHandler(nextValue);\n                        } else {\n                            resolve(nextValue);\n                        }\n                    });\n                }\n            };\n            promise.then(onFulfilled(promise), onRejected(promise));\n            registerCancelHandler(promise);\n        });\n        continuablePromiseMap.set(promise, continuablePromise);\n    }\n    return continuablePromise;\n};\nfunction useAtomValue(atom, options) {\n    const store = useStore(options);\n    const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((prev)=>{\n        const nextValue = store.get(atom);\n        if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n            return prev;\n        }\n        return [\n            nextValue,\n            store,\n            atom\n        ];\n    }, void 0, ()=>[\n            store.get(atom),\n            store,\n            atom\n        ]);\n    let value = valueFromReducer;\n    if (storeFromReducer !== store || atomFromReducer !== atom) {\n        rerender();\n        value = store.get(atom);\n    }\n    const delay = options == null ? void 0 : options.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const unsub = store.sub(atom, ()=>{\n            if (typeof delay === \"number\") {\n                const value2 = store.get(atom);\n                if (isPromiseLike(value2)) {\n                    attachPromiseMeta(createContinuablePromise(value2));\n                }\n                setTimeout(rerender, delay);\n                return;\n            }\n            rerender();\n        });\n        rerender();\n        return unsub;\n    }, [\n        store,\n        atom,\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(value);\n    if (isPromiseLike(value)) {\n        const promise = createContinuablePromise(value);\n        return use(promise);\n    }\n    return value;\n}\nfunction useSetAtom(atom, options) {\n    const store = useStore(options);\n    const setAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args)=>{\n        if (( false ? 0 : void 0) !== \"production\" && !(\"write\" in atom)) {\n            throw new Error(\"not writable atom\");\n        }\n        return store.set(atom, ...args);\n    }, [\n        store,\n        atom\n    ]);\n    return setAtom;\n}\nfunction useAtom(atom, options) {\n    return [\n        useAtomValue(atom, options),\n        // We do wrong type assertion here, which results in throwing an error.\n        useSetAtom(atom, options)\n    ];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla.mjs":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla.mjs ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   getDefaultStore: () => (/* binding */ getDefaultStore)\n/* harmony export */ });\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString() {\n      return ( false ? 0 : void 0) !== \"production\" && this.debugLabel ? key + \":\" + this.debugLabel : key;\n    }\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nconst isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst cancelablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst isPendingPromise = (value) => {\n  var _a;\n  return isPromiseLike(value) && !((_a = cancelablePromiseMap.get(value)) == null ? void 0 : _a[1]);\n};\nconst cancelPromise = (promise, nextValue) => {\n  const promiseState = cancelablePromiseMap.get(promise);\n  if (promiseState) {\n    promiseState[1] = true;\n    promiseState[0].forEach((fn) => fn(nextValue));\n  } else if (( false ? 0 : void 0) !== \"production\") {\n    throw new Error(\"[Bug] cancelable promise not found\");\n  }\n};\nconst patchPromiseForCancelability = (promise) => {\n  if (cancelablePromiseMap.has(promise)) {\n    return;\n  }\n  const promiseState = [/* @__PURE__ */ new Set(), false];\n  cancelablePromiseMap.set(promise, promiseState);\n  const settle = () => {\n    promiseState[1] = true;\n  };\n  promise.then(settle, settle);\n  promise.onCancel = (fn) => {\n    promiseState[0].add(fn);\n  };\n};\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nconst isAtomStateInitialized = (atomState) => \"v\" in atomState || \"e\" in atomState;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  if (( false ? 0 : void 0) !== \"production\" && !(\"v\" in atomState)) {\n    throw new Error(\"[Bug] atom state is not initialized\");\n  }\n  return atomState.v;\n};\nconst addPendingPromiseToDependency = (atom, promise, dependencyAtomState) => {\n  if (!dependencyAtomState.p.has(atom)) {\n    dependencyAtomState.p.add(atom);\n    promise.then(\n      () => {\n        dependencyAtomState.p.delete(atom);\n      },\n      () => {\n        dependencyAtomState.p.delete(atom);\n      }\n    );\n  }\n};\nconst addDependency = (pending, atom, atomState, a, aState) => {\n  var _a;\n  if (( false ? 0 : void 0) !== \"production\" && a === atom) {\n    throw new Error(\"[Bug] atom cannot depend on itself\");\n  }\n  atomState.d.set(a, aState.n);\n  if (isPendingPromise(atomState.v)) {\n    addPendingPromiseToDependency(atom, atomState.v, aState);\n  }\n  (_a = aState.m) == null ? void 0 : _a.t.add(atom);\n  if (pending) {\n    addPendingDependent(pending, a, atom);\n  }\n};\nconst createPending = () => [/* @__PURE__ */ new Map(), /* @__PURE__ */ new Map(), /* @__PURE__ */ new Set()];\nconst addPendingAtom = (pending, atom, atomState) => {\n  if (!pending[0].has(atom)) {\n    pending[0].set(atom, /* @__PURE__ */ new Set());\n  }\n  pending[1].set(atom, atomState);\n};\nconst addPendingDependent = (pending, atom, dependent) => {\n  const dependents = pending[0].get(atom);\n  if (dependents) {\n    dependents.add(dependent);\n  }\n};\nconst getPendingDependents = (pending, atom) => pending[0].get(atom);\nconst addPendingFunction = (pending, fn) => {\n  pending[2].add(fn);\n};\nconst flushPending = (pending) => {\n  let error;\n  let hasError = false;\n  const call = (fn) => {\n    try {\n      fn();\n    } catch (e) {\n      if (!hasError) {\n        error = e;\n        hasError = true;\n      }\n    }\n  };\n  while (pending[1].size || pending[2].size) {\n    pending[0].clear();\n    const atomStates = new Set(pending[1].values());\n    pending[1].clear();\n    const functions = new Set(pending[2]);\n    pending[2].clear();\n    atomStates.forEach((atomState) => {\n      var _a;\n      return (_a = atomState.m) == null ? void 0 : _a.l.forEach(call);\n    });\n    functions.forEach(call);\n  }\n  if (hasError) {\n    throw error;\n  }\n};\nconst buildStore = (...[getAtomState, atomRead, atomWrite, atomOnMount]) => {\n  let debugMountedAtoms;\n  if (( false ? 0 : void 0) !== \"production\") {\n    debugMountedAtoms = /* @__PURE__ */ new Set();\n  }\n  const setAtomStateValueOrPromise = (atom, atomState, valueOrPromise) => {\n    const hasPrevValue = \"v\" in atomState;\n    const prevValue = atomState.v;\n    const pendingPromise = isPendingPromise(atomState.v) ? atomState.v : null;\n    if (isPromiseLike(valueOrPromise)) {\n      patchPromiseForCancelability(valueOrPromise);\n      for (const a of atomState.d.keys()) {\n        addPendingPromiseToDependency(atom, valueOrPromise, getAtomState(a));\n      }\n      atomState.v = valueOrPromise;\n      delete atomState.e;\n    } else {\n      atomState.v = valueOrPromise;\n      delete atomState.e;\n    }\n    if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {\n      ++atomState.n;\n      if (pendingPromise) {\n        cancelPromise(pendingPromise, valueOrPromise);\n      }\n    }\n  };\n  const readAtomState = (pending, atom, dirtyAtoms) => {\n    var _a;\n    const atomState = getAtomState(atom);\n    if (isAtomStateInitialized(atomState)) {\n      if (atomState.m && !(dirtyAtoms == null ? void 0 : dirtyAtoms.has(atom))) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(\n        ([a, n]) => (\n          // Recursively, read the atom state of the dependency, and\n          // check if the atom epoch number is unchanged\n          readAtomState(pending, a, dirtyAtoms).n === n\n        )\n      )) {\n        return atomState;\n      }\n    }\n    atomState.d.clear();\n    let isSync = true;\n    const getter = (a) => {\n      if (isSelfAtom(atom, a)) {\n        const aState2 = getAtomState(a);\n        if (!isAtomStateInitialized(aState2)) {\n          if (hasInitialValue(a)) {\n            setAtomStateValueOrPromise(a, aState2, a.init);\n          } else {\n            throw new Error(\"no atom init\");\n          }\n        }\n        return returnAtomValue(aState2);\n      }\n      const aState = readAtomState(pending, a, dirtyAtoms);\n      try {\n        return returnAtomValue(aState);\n      } finally {\n        if (isSync) {\n          addDependency(pending, atom, atomState, a, aState);\n        } else {\n          const pending2 = createPending();\n          addDependency(pending2, atom, atomState, a, aState);\n          mountDependencies(pending2, atom, atomState);\n          flushPending(pending2);\n        }\n      }\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if (( false ? 0 : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if (( false ? 0 : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              return writeAtom(atom, ...args);\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    try {\n      const valueOrPromise = atomRead(atom, getter, options);\n      setAtomStateValueOrPromise(atom, atomState, valueOrPromise);\n      if (isPromiseLike(valueOrPromise)) {\n        (_a = valueOrPromise.onCancel) == null ? void 0 : _a.call(valueOrPromise, () => controller == null ? void 0 : controller.abort());\n        const complete = () => {\n          if (atomState.m) {\n            const pending2 = createPending();\n            mountDependencies(pending2, atom, atomState);\n            flushPending(pending2);\n          }\n        };\n        valueOrPromise.then(complete, complete);\n      }\n      return atomState;\n    } catch (error) {\n      delete atomState.v;\n      atomState.e = error;\n      ++atomState.n;\n      return atomState;\n    } finally {\n      isSync = false;\n    }\n  };\n  const readAtom = (atom) => returnAtomValue(readAtomState(void 0, atom));\n  const getMountedOrPendingDependents = (pending, atom, atomState) => {\n    var _a, _b;\n    const dependents = /* @__PURE__ */ new Map();\n    for (const a of ((_a = atomState.m) == null ? void 0 : _a.t) || []) {\n      const aState = getAtomState(a);\n      if (aState.m) {\n        dependents.set(a, aState);\n      }\n    }\n    for (const atomWithPendingPromise of atomState.p) {\n      dependents.set(\n        atomWithPendingPromise,\n        getAtomState(atomWithPendingPromise)\n      );\n    }\n    (_b = getPendingDependents(pending, atom)) == null ? void 0 : _b.forEach((dependent) => {\n      dependents.set(dependent, getAtomState(dependent));\n    });\n    return dependents;\n  };\n  function getSortedDependents(pending, rootAtom, rootAtomState) {\n    const sorted = [];\n    const visiting = /* @__PURE__ */ new Set();\n    const visited = /* @__PURE__ */ new Set();\n    const stack = [[rootAtom, rootAtomState]];\n    while (stack.length > 0) {\n      const [a, aState] = stack[stack.length - 1];\n      if (visited.has(a)) {\n        stack.pop();\n        continue;\n      }\n      if (visiting.has(a)) {\n        sorted.push([a, aState, aState.n]);\n        visited.add(a);\n        stack.pop();\n        continue;\n      }\n      visiting.add(a);\n      for (const [d, s] of getMountedOrPendingDependents(pending, a, aState)) {\n        if (a !== d && !visiting.has(d)) {\n          stack.push([d, s]);\n        }\n      }\n    }\n    return [sorted, visited];\n  }\n  const recomputeDependents = (pending, atom, atomState) => {\n    const [topsortedAtoms, markedAtoms] = getSortedDependents(\n      pending,\n      atom,\n      atomState\n    );\n    const changedAtoms = /* @__PURE__ */ new Set([atom]);\n    for (let i = topsortedAtoms.length - 1; i >= 0; --i) {\n      const [a, aState, prevEpochNumber] = topsortedAtoms[i];\n      let hasChangedDeps = false;\n      for (const dep of aState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        readAtomState(pending, a, markedAtoms);\n        mountDependencies(pending, a, aState);\n        if (prevEpochNumber !== aState.n) {\n          addPendingAtom(pending, a, aState);\n          changedAtoms.add(a);\n        }\n      }\n      markedAtoms.delete(a);\n    }\n  };\n  const writeAtomState = (pending, atom, ...args) => {\n    let isSync = true;\n    const getter = (a) => returnAtomValue(readAtomState(pending, a));\n    const setter = (a, ...args2) => {\n      const aState = getAtomState(a);\n      try {\n        if (isSelfAtom(atom, a)) {\n          if (!hasInitialValue(a)) {\n            throw new Error(\"atom not writable\");\n          }\n          const prevEpochNumber = aState.n;\n          const v = args2[0];\n          setAtomStateValueOrPromise(a, aState, v);\n          mountDependencies(pending, a, aState);\n          if (prevEpochNumber !== aState.n) {\n            addPendingAtom(pending, a, aState);\n            recomputeDependents(pending, a, aState);\n          }\n          return void 0;\n        } else {\n          return writeAtomState(pending, a, ...args2);\n        }\n      } finally {\n        if (!isSync) {\n          flushPending(pending);\n        }\n      }\n    };\n    try {\n      return atomWrite(atom, getter, setter, ...args);\n    } finally {\n      isSync = false;\n    }\n  };\n  const writeAtom = (atom, ...args) => {\n    const pending = createPending();\n    try {\n      return writeAtomState(pending, atom, ...args);\n    } finally {\n      flushPending(pending);\n    }\n  };\n  const mountDependencies = (pending, atom, atomState) => {\n    if (atomState.m && !isPendingPromise(atomState.v)) {\n      for (const a of atomState.d.keys()) {\n        if (!atomState.m.d.has(a)) {\n          const aMounted = mountAtom(pending, a, getAtomState(a));\n          aMounted.t.add(atom);\n          atomState.m.d.add(a);\n        }\n      }\n      for (const a of atomState.m.d || []) {\n        if (!atomState.d.has(a)) {\n          atomState.m.d.delete(a);\n          const aMounted = unmountAtom(pending, a, getAtomState(a));\n          aMounted == null ? void 0 : aMounted.t.delete(atom);\n        }\n      }\n    }\n  };\n  const mountAtom = (pending, atom, atomState) => {\n    if (!atomState.m) {\n      readAtomState(pending, atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = mountAtom(pending, a, getAtomState(a));\n        aMounted.t.add(atom);\n      }\n      atomState.m = {\n        l: /* @__PURE__ */ new Set(),\n        d: new Set(atomState.d.keys()),\n        t: /* @__PURE__ */ new Set()\n      };\n      if (( false ? 0 : void 0) !== \"production\") {\n        debugMountedAtoms.add(atom);\n      }\n      if (isActuallyWritableAtom(atom)) {\n        const mounted = atomState.m;\n        let setAtom;\n        const createInvocationContext = (pending2, fn) => {\n          let isSync = true;\n          setAtom = (...args) => {\n            try {\n              return writeAtomState(pending2, atom, ...args);\n            } finally {\n              if (!isSync) {\n                flushPending(pending2);\n              }\n            }\n          };\n          try {\n            return fn();\n          } finally {\n            isSync = false;\n          }\n        };\n        addPendingFunction(pending, () => {\n          const onUnmount = createInvocationContext(\n            pending,\n            () => atomOnMount(atom, (...args) => setAtom(...args))\n          );\n          if (onUnmount) {\n            mounted.u = (pending2) => createInvocationContext(pending2, onUnmount);\n          }\n        });\n      }\n    }\n    return atomState.m;\n  };\n  const unmountAtom = (pending, atom, atomState) => {\n    if (atomState.m && !atomState.m.l.size && !Array.from(atomState.m.t).some((a) => {\n      var _a;\n      return (_a = getAtomState(a).m) == null ? void 0 : _a.d.has(atom);\n    })) {\n      const onUnmount = atomState.m.u;\n      if (onUnmount) {\n        addPendingFunction(pending, () => onUnmount(pending));\n      }\n      delete atomState.m;\n      if (( false ? 0 : void 0) !== \"production\") {\n        debugMountedAtoms.delete(atom);\n      }\n      for (const a of atomState.d.keys()) {\n        const aMounted = unmountAtom(pending, a, getAtomState(a));\n        aMounted == null ? void 0 : aMounted.t.delete(atom);\n      }\n      return void 0;\n    }\n    return atomState.m;\n  };\n  const subscribeAtom = (atom, listener) => {\n    const pending = createPending();\n    const atomState = getAtomState(atom);\n    const mounted = mountAtom(pending, atom, atomState);\n    const listeners = mounted.l;\n    listeners.add(listener);\n    flushPending(pending);\n    return () => {\n      listeners.delete(listener);\n      const pending2 = createPending();\n      unmountAtom(pending2, atom, atomState);\n      flushPending(pending2);\n    };\n  };\n  const unstable_derive = (fn) => buildStore(...fn(getAtomState, atomRead, atomWrite, atomOnMount));\n  const store = {\n    get: readAtom,\n    set: writeAtom,\n    sub: subscribeAtom,\n    unstable_derive\n  };\n  if (( false ? 0 : void 0) !== \"production\") {\n    const devStore = {\n      // store dev methods (these are tentative and subject to change without notice)\n      dev4_get_internal_weak_map: () => ({\n        get: (atom) => {\n          const atomState = getAtomState(atom);\n          if (atomState.n === 0) {\n            return void 0;\n          }\n          return atomState;\n        }\n      }),\n      dev4_get_mounted_atoms: () => debugMountedAtoms,\n      dev4_restore_atoms: (values) => {\n        const pending = createPending();\n        for (const [atom, value] of values) {\n          if (hasInitialValue(atom)) {\n            const atomState = getAtomState(atom);\n            const prevEpochNumber = atomState.n;\n            setAtomStateValueOrPromise(atom, atomState, value);\n            mountDependencies(pending, atom, atomState);\n            if (prevEpochNumber !== atomState.n) {\n              addPendingAtom(pending, atom, atomState);\n              recomputeDependents(pending, atom, atomState);\n            }\n          }\n        }\n        flushPending(pending);\n      }\n    };\n    Object.assign(store, devStore);\n  }\n  return store;\n};\nconst createStore = () => {\n  const atomStateMap = /* @__PURE__ */ new WeakMap();\n  const getAtomState = (atom) => {\n    if (( false ? 0 : void 0) !== \"production\" && !atom) {\n      throw new Error(\"Atom is undefined or null\");\n    }\n    let atomState = atomStateMap.get(atom);\n    if (!atomState) {\n      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };\n      atomStateMap.set(atom, atomState);\n    }\n    return atomState;\n  };\n  return buildStore(\n    getAtomState,\n    (atom, ...params) => atom.read(...params),\n    (atom, ...params) => atom.write(...params),\n    (atom, ...params) => {\n      var _a;\n      return (_a = atom.onMount) == null ? void 0 : _a.call(atom, ...params);\n    }\n  );\n};\nlet defaultStore;\nconst getDefaultStore = () => {\n  if (!defaultStore) {\n    defaultStore = createStore();\n    if (( false ? 0 : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla/utils.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla/utils.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESET: () => (/* binding */ RESET),\n/* harmony export */   atomFamily: () => (/* binding */ atomFamily),\n/* harmony export */   atomWithDefault: () => (/* binding */ atomWithDefault),\n/* harmony export */   atomWithLazy: () => (/* binding */ atomWithLazy),\n/* harmony export */   atomWithObservable: () => (/* binding */ atomWithObservable),\n/* harmony export */   atomWithReducer: () => (/* binding */ atomWithReducer),\n/* harmony export */   atomWithRefresh: () => (/* binding */ atomWithRefresh),\n/* harmony export */   atomWithReset: () => (/* binding */ atomWithReset),\n/* harmony export */   atomWithStorage: () => (/* binding */ atomWithStorage),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   freezeAtom: () => (/* binding */ freezeAtom),\n/* harmony export */   freezeAtomCreator: () => (/* binding */ freezeAtomCreator),\n/* harmony export */   loadable: () => (/* binding */ loadable),\n/* harmony export */   selectAtom: () => (/* binding */ selectAtom),\n/* harmony export */   splitAtom: () => (/* binding */ splitAtom),\n/* harmony export */   unstable_withStorageValidator: () => (/* binding */ withStorageValidator),\n/* harmony export */   unwrap: () => (/* binding */ unwrap)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla.mjs\");\n\n\nconst RESET = Symbol(\n  ( false ? 0 : void 0) !== \"production\" ? \"RESET\" : \"\"\n);\n\nfunction atomWithReset(initialValue) {\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    initialValue,\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(anAtom)) : update;\n      set(anAtom, nextValue === RESET ? initialValue : nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithReducer(initialValue, reducer) {\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(initialValue, function(get, set, action) {\n    set(this, reducer(get(this), action));\n  });\n}\n\nfunction atomFamily(initializeAtom, areEqual) {\n  let shouldRemove = null;\n  const atoms = /* @__PURE__ */ new Map();\n  const listeners = /* @__PURE__ */ new Set();\n  const createAtom = (param) => {\n    let item;\n    if (areEqual === void 0) {\n      item = atoms.get(param);\n    } else {\n      for (const [key, value] of atoms) {\n        if (areEqual(key, param)) {\n          item = value;\n          break;\n        }\n      }\n    }\n    if (item !== void 0) {\n      if (shouldRemove == null ? void 0 : shouldRemove(item[1], param)) {\n        createAtom.remove(param);\n      } else {\n        return item[0];\n      }\n    }\n    const newAtom = initializeAtom(param);\n    atoms.set(param, [newAtom, Date.now()]);\n    notifyListeners(\"CREATE\", param, newAtom);\n    return newAtom;\n  };\n  function notifyListeners(type, param, atom) {\n    for (const listener of listeners) {\n      listener({ type, param, atom });\n    }\n  }\n  createAtom.unstable_listen = (callback) => {\n    listeners.add(callback);\n    return () => {\n      listeners.delete(callback);\n    };\n  };\n  createAtom.getParams = () => atoms.keys();\n  createAtom.remove = (param) => {\n    if (areEqual === void 0) {\n      if (!atoms.has(param)) return;\n      const [atom] = atoms.get(param);\n      atoms.delete(param);\n      notifyListeners(\"REMOVE\", param, atom);\n    } else {\n      for (const [key, [atom]] of atoms) {\n        if (areEqual(key, param)) {\n          atoms.delete(key);\n          notifyListeners(\"REMOVE\", key, atom);\n          break;\n        }\n      }\n    }\n  };\n  createAtom.setShouldRemove = (fn) => {\n    shouldRemove = fn;\n    if (!shouldRemove) return;\n    for (const [key, [atom, createdAt]] of atoms) {\n      if (shouldRemove(createdAt, key)) {\n        atoms.delete(key);\n        notifyListeners(\"REMOVE\", key, atom);\n      }\n    }\n  };\n  return createAtom;\n}\n\nconst getCached$2 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$3 = /* @__PURE__ */ new WeakMap();\nconst memo3 = (create, dep1, dep2, dep3) => {\n  const cache2 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache1$3, dep1);\n  const cache3 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache2, dep2);\n  return getCached$2(create, cache3, dep3);\n};\nfunction selectAtom(anAtom, selector, equalityFn = Object.is) {\n  return memo3(\n    () => {\n      const EMPTY = Symbol();\n      const selectValue = ([value, prevSlice]) => {\n        if (prevSlice === EMPTY) {\n          return selector(value);\n        }\n        const slice = selector(value, prevSlice);\n        return equalityFn(prevSlice, slice) ? prevSlice : slice;\n      };\n      const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(derivedAtom);\n        const value = get(anAtom);\n        return selectValue([value, prev]);\n      });\n      derivedAtom.init = EMPTY;\n      return derivedAtom;\n    },\n    anAtom,\n    selector,\n    equalityFn\n  );\n}\n\nconst frozenAtoms = /* @__PURE__ */ new WeakSet();\nconst deepFreeze = (obj) => {\n  if (typeof obj !== \"object\" || obj === null) return;\n  Object.freeze(obj);\n  const propNames = Object.getOwnPropertyNames(obj);\n  for (const name of propNames) {\n    const value = obj[name];\n    deepFreeze(value);\n  }\n  return obj;\n};\nfunction freezeAtom(anAtom) {\n  if (frozenAtoms.has(anAtom)) {\n    return anAtom;\n  }\n  frozenAtoms.add(anAtom);\n  const origRead = anAtom.read;\n  anAtom.read = function(get, options) {\n    return deepFreeze(origRead.call(this, get, options));\n  };\n  if (\"write\" in anAtom) {\n    const origWrite = anAtom.write;\n    anAtom.write = function(get, set, ...args) {\n      return origWrite.call(\n        this,\n        get,\n        (...setArgs) => {\n          if (setArgs[0] === anAtom) {\n            setArgs[1] = deepFreeze(setArgs[1]);\n          }\n          return set(...setArgs);\n        },\n        ...args\n      );\n    };\n  }\n  return anAtom;\n}\nfunction freezeAtomCreator(createAtom) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] freezeAtomCreator is deprecated, define it on users end\"\n    );\n  }\n  return (...args) => freezeAtom(createAtom(...args));\n}\n\nconst getCached$1 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$2 = /* @__PURE__ */ new WeakMap();\nconst memo2$1 = (create, dep1, dep2) => {\n  const cache2 = getCached$1(() => /* @__PURE__ */ new WeakMap(), cache1$2, dep1);\n  return getCached$1(create, cache2, dep2);\n};\nconst cacheKeyForEmptyKeyExtractor = {};\nconst isWritable = (atom2) => !!atom2.write;\nconst isFunction = (x) => typeof x === \"function\";\nfunction splitAtom(arrAtom, keyExtractor) {\n  return memo2$1(\n    () => {\n      const mappingCache = /* @__PURE__ */ new WeakMap();\n      const getMapping = (arr, prev) => {\n        let mapping = mappingCache.get(arr);\n        if (mapping) {\n          return mapping;\n        }\n        const prevMapping = prev && mappingCache.get(prev);\n        const atomList = [];\n        const keyList = [];\n        arr.forEach((item, index) => {\n          const key = keyExtractor ? keyExtractor(item) : index;\n          keyList[index] = key;\n          const cachedAtom = prevMapping && prevMapping.atomList[prevMapping.keyList.indexOf(key)];\n          if (cachedAtom) {\n            atomList[index] = cachedAtom;\n            return;\n          }\n          const read = (get) => {\n            const prev2 = get(mappingAtom);\n            const currArr = get(arrAtom);\n            const mapping2 = getMapping(currArr, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= currArr.length) {\n              const prevItem = arr[getMapping(arr).keyList.indexOf(key)];\n              if (prevItem) {\n                return prevItem;\n              }\n              throw new Error(\"splitAtom: index out of bounds for read\");\n            }\n            return currArr[index2];\n          };\n          const write = (get, set, update) => {\n            const prev2 = get(mappingAtom);\n            const arr2 = get(arrAtom);\n            const mapping2 = getMapping(arr2, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= arr2.length) {\n              throw new Error(\"splitAtom: index out of bounds for write\");\n            }\n            const nextItem = isFunction(update) ? update(arr2[index2]) : update;\n            if (!Object.is(arr2[index2], nextItem)) {\n              set(arrAtom, [\n                ...arr2.slice(0, index2),\n                nextItem,\n                ...arr2.slice(index2 + 1)\n              ]);\n            }\n          };\n          atomList[index] = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read, write) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read);\n        });\n        if (prevMapping && prevMapping.keyList.length === keyList.length && prevMapping.keyList.every((x, i) => x === keyList[i])) {\n          mapping = prevMapping;\n        } else {\n          mapping = { arr, atomList, keyList };\n        }\n        mappingCache.set(arr, mapping);\n        return mapping;\n      };\n      const mappingAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(mappingAtom);\n        const arr = get(arrAtom);\n        const mapping = getMapping(arr, prev == null ? void 0 : prev.arr);\n        return mapping;\n      });\n      if (( false ? 0 : void 0) !== \"production\") {\n        mappingAtom.debugPrivate = true;\n      }\n      mappingAtom.init = void 0;\n      const splittedAtom = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => get(mappingAtom).atomList,\n        (get, set, action) => {\n          switch (action.type) {\n            case \"remove\": {\n              const index = get(splittedAtom).indexOf(action.atom);\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  ...arr.slice(index + 1)\n                ]);\n              }\n              break;\n            }\n            case \"insert\": {\n              const index = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  action.value,\n                  ...arr.slice(index)\n                ]);\n              }\n              break;\n            }\n            case \"move\": {\n              const index1 = get(splittedAtom).indexOf(action.atom);\n              const index2 = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index1 >= 0 && index2 >= 0) {\n                const arr = get(arrAtom);\n                if (index1 < index2) {\n                  set(arrAtom, [\n                    ...arr.slice(0, index1),\n                    ...arr.slice(index1 + 1, index2),\n                    arr[index1],\n                    ...arr.slice(index2)\n                  ]);\n                } else {\n                  set(arrAtom, [\n                    ...arr.slice(0, index2),\n                    arr[index1],\n                    ...arr.slice(index2, index1),\n                    ...arr.slice(index1 + 1)\n                  ]);\n                }\n              }\n              break;\n            }\n          }\n        }\n      ) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(mappingAtom).atomList);\n      return splittedAtom;\n    },\n    arrAtom,\n    keyExtractor || cacheKeyForEmptyKeyExtractor\n  );\n}\n\nfunction atomWithDefault(getDefault) {\n  const EMPTY = Symbol();\n  const overwrittenAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(EMPTY);\n  if (( false ? 0 : void 0) !== \"production\") {\n    overwrittenAtom.debugPrivate = true;\n  }\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      const overwritten = get(overwrittenAtom);\n      if (overwritten !== EMPTY) {\n        return overwritten;\n      }\n      return getDefault(get, options);\n    },\n    (get, set, update) => {\n      if (update === RESET) {\n        set(overwrittenAtom, EMPTY);\n      } else if (typeof update === \"function\") {\n        const prevValue = get(anAtom);\n        set(overwrittenAtom, update(prevValue));\n      } else {\n        set(overwrittenAtom, update);\n      }\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction withStorageValidator(validator) {\n  return (unknownStorage) => {\n    const storage = {\n      ...unknownStorage,\n      getItem: (key, initialValue) => {\n        const validate = (value2) => {\n          if (!validator(value2)) {\n            return initialValue;\n          }\n          return value2;\n        };\n        const value = unknownStorage.getItem(key, initialValue);\n        if (isPromiseLike(value)) {\n          return value.then(validate);\n        }\n        return validate(value);\n      }\n    };\n    return storage;\n  };\n}\nfunction createJSONStorage(getStringStorage = () => {\n  try {\n    return window.localStorage;\n  } catch (e) {\n    if (( false ? 0 : void 0) !== \"production\") {\n      if (typeof window !== \"undefined\") {\n        console.warn(e);\n      }\n    }\n    return void 0;\n  }\n}, options) {\n  var _a;\n  let lastStr;\n  let lastValue;\n  const storage = {\n    getItem: (key, initialValue) => {\n      var _a2, _b;\n      const parse = (str2) => {\n        str2 = str2 || \"\";\n        if (lastStr !== str2) {\n          try {\n            lastValue = JSON.parse(str2, options == null ? void 0 : options.reviver);\n          } catch (e) {\n            return initialValue;\n          }\n          lastStr = str2;\n        }\n        return lastValue;\n      };\n      const str = (_b = (_a2 = getStringStorage()) == null ? void 0 : _a2.getItem(key)) != null ? _b : null;\n      if (isPromiseLike(str)) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (key, newValue) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.setItem(\n        key,\n        JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n      );\n    },\n    removeItem: (key) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.removeItem(key);\n    }\n  };\n  const createHandleSubscribe = (subscriber2) => (key, callback, initialValue) => subscriber2(key, (v) => {\n    let newValue;\n    try {\n      newValue = JSON.parse(v || \"\");\n    } catch (e) {\n      newValue = initialValue;\n    }\n    callback(newValue);\n  });\n  let subscriber;\n  try {\n    subscriber = (_a = getStringStorage()) == null ? void 0 : _a.subscribe;\n  } catch (e) {\n  }\n  if (!subscriber && typeof window !== \"undefined\" && typeof window.addEventListener === \"function\" && window.Storage) {\n    subscriber = (key, callback) => {\n      if (!(getStringStorage() instanceof window.Storage)) {\n        return () => {\n        };\n      }\n      const storageEventCallback = (e) => {\n        if (e.storageArea === getStringStorage() && e.key === key) {\n          callback(e.newValue);\n        }\n      };\n      window.addEventListener(\"storage\", storageEventCallback);\n      return () => {\n        window.removeEventListener(\"storage\", storageEventCallback);\n      };\n    };\n  }\n  if (subscriber) {\n    storage.subscribe = createHandleSubscribe(subscriber);\n  }\n  return storage;\n}\nconst defaultStorage = createJSONStorage();\nfunction atomWithStorage(key, initialValue, storage = defaultStorage, options) {\n  const getOnInit = options == null ? void 0 : options.getOnInit;\n  const baseAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    getOnInit ? storage.getItem(key, initialValue) : initialValue\n  );\n  if (( false ? 0 : void 0) !== \"production\") {\n    baseAtom.debugPrivate = true;\n  }\n  baseAtom.onMount = (setAtom) => {\n    setAtom(storage.getItem(key, initialValue));\n    let unsub;\n    if (storage.subscribe) {\n      unsub = storage.subscribe(key, setAtom, initialValue);\n    }\n    return unsub;\n  };\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => get(baseAtom),\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(baseAtom)) : update;\n      if (nextValue === RESET) {\n        set(baseAtom, initialValue);\n        return storage.removeItem(key);\n      }\n      if (nextValue instanceof Promise) {\n        return nextValue.then((resolvedValue) => {\n          set(baseAtom, resolvedValue);\n          return storage.setItem(key, resolvedValue);\n        });\n      }\n      set(baseAtom, nextValue);\n      return storage.setItem(key, nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithObservable(getObservable, options) {\n  const returnResultData = (result) => {\n    if (\"e\" in result) {\n      throw result.e;\n    }\n    return result.d;\n  };\n  const observableResultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n    var _a;\n    let observable = getObservable(get);\n    const itself = (_a = observable[Symbol.observable]) == null ? void 0 : _a.call(observable);\n    if (itself) {\n      observable = itself;\n    }\n    let resolve;\n    const makePending = () => new Promise((r) => {\n      resolve = r;\n    });\n    const initialResult = options && \"initialValue\" in options ? {\n      d: typeof options.initialValue === \"function\" ? options.initialValue() : options.initialValue\n    } : makePending();\n    let setResult;\n    let lastResult;\n    const listener = (result) => {\n      lastResult = result;\n      resolve == null ? void 0 : resolve(result);\n      setResult == null ? void 0 : setResult(result);\n    };\n    let subscription;\n    let timer;\n    const isNotMounted = () => !setResult;\n    const start = () => {\n      if (subscription) {\n        clearTimeout(timer);\n        subscription.unsubscribe();\n      }\n      subscription = observable.subscribe({\n        next: (d) => listener({ d }),\n        error: (e) => listener({ e }),\n        complete: () => {\n        }\n      });\n      if (isNotMounted() && (options == null ? void 0 : options.unstable_timeout)) {\n        timer = setTimeout(() => {\n          if (subscription) {\n            subscription.unsubscribe();\n            subscription = void 0;\n          }\n        }, options.unstable_timeout);\n      }\n    };\n    start();\n    const resultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(lastResult || initialResult);\n    if (( false ? 0 : void 0) !== \"production\") {\n      resultAtom.debugPrivate = true;\n    }\n    resultAtom.onMount = (update) => {\n      setResult = update;\n      if (lastResult) {\n        update(lastResult);\n      }\n      if (subscription) {\n        clearTimeout(timer);\n      } else {\n        start();\n      }\n      return () => {\n        setResult = void 0;\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = void 0;\n        }\n      };\n    };\n    return [resultAtom, observable, makePending, start, isNotMounted];\n  });\n  if (( false ? 0 : void 0) !== \"production\") {\n    observableResultAtom.debugPrivate = true;\n  }\n  const observableAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => {\n      const [resultAtom] = get(observableResultAtom);\n      const result = get(resultAtom);\n      if (result instanceof Promise) {\n        return result.then(returnResultData);\n      }\n      return returnResultData(result);\n    },\n    (get, set, data) => {\n      const [resultAtom, observable, makePending, start, isNotMounted] = get(observableResultAtom);\n      if (\"next\" in observable) {\n        if (isNotMounted()) {\n          set(resultAtom, makePending());\n          start();\n        }\n        observable.next(data);\n      } else {\n        throw new Error(\"observable is not subject\");\n      }\n    }\n  );\n  return observableAtom;\n}\n\nconst cache1$1 = /* @__PURE__ */ new WeakMap();\nconst memo1 = (create, dep1) => (cache1$1.has(dep1) ? cache1$1 : cache1$1.set(dep1, create())).get(dep1);\nconst isPromise$1 = (x) => x instanceof Promise;\nconst LOADING = { state: \"loading\" };\nfunction loadable(anAtom) {\n  return memo1(() => {\n    const loadableCache = /* @__PURE__ */ new WeakMap();\n    const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n    if (( false ? 0 : void 0) !== \"production\") {\n      refreshAtom.debugPrivate = true;\n    }\n    const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n      (get, { setSelf }) => {\n        get(refreshAtom);\n        let value;\n        try {\n          value = get(anAtom);\n        } catch (error) {\n          return { state: \"hasError\", error };\n        }\n        if (!isPromise$1(value)) {\n          return { state: \"hasData\", data: value };\n        }\n        const promise = value;\n        const cached1 = loadableCache.get(promise);\n        if (cached1) {\n          return cached1;\n        }\n        promise.then(\n          (data) => {\n            loadableCache.set(promise, { state: \"hasData\", data });\n            setSelf();\n          },\n          (error) => {\n            loadableCache.set(promise, { state: \"hasError\", error });\n            setSelf();\n          }\n        );\n        const cached2 = loadableCache.get(promise);\n        if (cached2) {\n          return cached2;\n        }\n        loadableCache.set(promise, LOADING);\n        return LOADING;\n      },\n      (_get, set) => {\n        set(refreshAtom, (c) => c + 1);\n      }\n    );\n    if (( false ? 0 : void 0) !== \"production\") {\n      derivedAtom.debugPrivate = true;\n    }\n    return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(derivedAtom));\n  }, anAtom);\n}\n\nconst getCached = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1 = /* @__PURE__ */ new WeakMap();\nconst memo2 = (create, dep1, dep2) => {\n  const cache2 = getCached(() => /* @__PURE__ */ new WeakMap(), cache1, dep1);\n  return getCached(create, cache2, dep2);\n};\nconst isPromise = (x) => x instanceof Promise;\nconst defaultFallback = () => void 0;\nfunction unwrap(anAtom, fallback = defaultFallback) {\n  return memo2(\n    () => {\n      const promiseErrorCache = /* @__PURE__ */ new WeakMap();\n      const promiseResultCache = /* @__PURE__ */ new WeakMap();\n      const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n      if (( false ? 0 : void 0) !== \"production\") {\n        refreshAtom.debugPrivate = true;\n      }\n      const promiseAndValueAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get, { setSelf }) => {\n          get(refreshAtom);\n          const prev = get(promiseAndValueAtom);\n          const promise = get(anAtom);\n          if (!isPromise(promise)) {\n            return { v: promise };\n          }\n          if (promise !== (prev == null ? void 0 : prev.p)) {\n            promise.then(\n              (v) => {\n                promiseResultCache.set(promise, v);\n                setSelf();\n              },\n              (e) => {\n                promiseErrorCache.set(promise, e);\n                setSelf();\n              }\n            );\n          }\n          if (promiseErrorCache.has(promise)) {\n            throw promiseErrorCache.get(promise);\n          }\n          if (promiseResultCache.has(promise)) {\n            return {\n              p: promise,\n              v: promiseResultCache.get(promise)\n            };\n          }\n          if (prev && \"v\" in prev) {\n            return { p: promise, f: fallback(prev.v), v: prev.v };\n          }\n          return { p: promise, f: fallback() };\n        },\n        (_get, set) => {\n          set(refreshAtom, (c) => c + 1);\n        }\n      );\n      promiseAndValueAtom.init = void 0;\n      if (( false ? 0 : void 0) !== \"production\") {\n        promiseAndValueAtom.debugPrivate = true;\n      }\n      return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => {\n          const state = get(promiseAndValueAtom);\n          if (\"f\" in state) {\n            return state.f;\n          }\n          return state.v;\n        },\n        (_get, set, ...args) => set(anAtom, ...args)\n      );\n    },\n    anAtom,\n    fallback\n  );\n}\n\nfunction atomWithRefresh(read, write) {\n  const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n  if (( false ? 0 : void 0) !== \"production\") {\n    refreshAtom.debugPrivate = true;\n  }\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      get(refreshAtom);\n      return read(get, options);\n    },\n    (get, set, ...args) => {\n      if (args.length === 0) {\n        set(refreshAtom, (c) => c + 1);\n      } else if (write) {\n        return write(get, set, ...args);\n      }\n    }\n  );\n}\n\nfunction atomWithLazy(makeInitial) {\n  const a = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(void 0);\n  delete a.init;\n  Object.defineProperty(a, \"init\", {\n    get() {\n      return makeInitial();\n    }\n  });\n  return a;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.10.4_@types+react@18.3.17_react@18.3.1/node_modules/jotai/esm/vanilla/utils.mjs\n");

/***/ })

};
;