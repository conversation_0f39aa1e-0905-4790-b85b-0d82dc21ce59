"use client";
import React, { useEffect } from "react";
import { Card } from "@/components/ui/card";
import { VideoPreviewFrame, ConfigurationPanel } from "./components";
import { useCreatePage } from "./hooks/useCreatePage";
import { appConfigAtom, store } from "@/stores";
import { useAtom } from "jotai";
import { voiceStoreAtom } from "@/stores/slices/voice_store";
import ky from "ky";
import { env } from "@/env";
import { voices, VoiceOption } from "@/constants/voices";

async function fetchTTSProviders(apiKey: string | undefined) {
  if (!apiKey) {
    return;
  }

  try {
    const response = await ky.get(
      `${env.NEXT_PUBLIC_API_URL}/302/tts/provider`,
      {
        headers: { Authorization: `Bearer ${apiKey}` },
      }
    );

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (err: any) {
    const errorText = await err.response.text();
    const errorData = JSON.parse(errorText);
    if (errorData.error && errorData.error.err_code) {
      // toast.error(() => ErrorToast(errorData.error.err_code));
    } else {
      // toast.error("获取供应商失败");
    }
  }
}

const CreatePage = () => {
  const { state, updateSmallSelect, updateLargeSelect, updateTextContent } =
    useCreatePage();

  const { apiKey } = store.get(appConfigAtom);
  const [voiceStore, setVoiceStore] = useAtom(voiceStoreAtom);

  useEffect(() => {
    (async () => {
      const providerData = await fetchTTSProviders(apiKey);

      if (!providerData) {
        return;
      }

      const { provider_list } = providerData;

      // 处理 doubao 数据
      const doubaoProvider = provider_list.find(
        (p) => p.provider.toLowerCase() === "doubao"
      );
      if (doubaoProvider) {
        const doubaoVoiceList = doubaoProvider.req_params_info.voice_list || [];
        const doubaoVoiceOptions = doubaoVoiceList.map((voice) => ({
          key: voice.voice,
          label: `${voice.name} ${
            voice.gender ? `(${t(`common.${voice.gender.toLowerCase()}`)})` : ""
          }`,
          value: voice.voice,
          originData: voice,
        }));

        // 更新 voices 中的 Doubao children
        const doubaoVoice = voices.find((v) => v.key === "Doubao");
        if (doubaoVoice) {
          doubaoVoice.children = doubaoVoiceOptions;
        }
      }

      // 处理 fish 数据
      const fishProvider = provider_list.find(
        (p) => p.provider.toLowerCase() === "fish"
      );
      if (fishProvider) {
        const fishVoiceList = fishProvider.req_params_info.voice_list || [];
        const fishVoiceOptions = fishVoiceList.map((voice) => ({
          key: voice.voice,
          label: voice.name || voice.voice,
          value: voice.voice,
          originData: voice,
        }));

        // 更新 voices 中的 FishAudio children
        const fishVoice = voices.find((v) => v.key === "fish");
        if (fishVoice) {
          fishVoice.children = fishVoiceOptions;
        }
      }

      // 处理 minimax 数据
      const minimaxProvider = provider_list.find(
        (p) => p.provider.toLowerCase() === "minimaxi"
      );
      if (minimaxProvider) {
        const minimaxVoiceList =
          minimaxProvider.req_params_info.voice_list || [];
        const minimaxVoiceOptions: VoiceOption[] = minimaxVoiceList.map(
          (voice) => ({
            key: voice.voice,
            label: `${voice.name} ${
              voice.gender
                ? `(${t(`common.${voice.gender.toLowerCase()}`)})`
                : ""
            }`,
            value: voice.voice,
            originData: voice,
          })
        );

        // 更新 voices 中的 Minimax children
        const minimaxVoice = voices.find((v) => v.key === "Minimaxi");
        if (minimaxVoice) {
          minimaxVoice.children = minimaxVoiceOptions;
        }
      }

      // // 处理 dubbingxi 数据
      // const dubbingxiProvider = provider_list.find(
      //   (p) => p.provider.toLowerCase() === "dubbingx"
      // );
      // if (dubbingxiProvider) {
      //   const dubbingxiVoiceList =
      //     dubbingxiProvider.req_params_info.voice_list || [];
      //   const dubbingxiVoiceOptions: VoiceOption[] = dubbingxiVoiceList.map(
      //     (voice) => ({
      //       key: voice.voice,
      //       label: `${voice.name} (${t(voice.gender.toLowerCase())})`,
      //       value: voice.voice,
      //       originData: voice,
      //     })
      //   );

      //   // 更新 voices 中的 dubbingxi children
      //   const dubbingxiVoice = voices.find((v) => v.key === "dubbingx");
      //   if (dubbingxiVoice) {
      //     dubbingxiVoice.children = dubbingxiVoiceOptions;
      //   }
      // }

      // // 处理 elevenlabs 数据
      // const elevenlabsProvider = provider_list.find(
      //   (p) => p.provider.toLowerCase() === "elevenlabs"
      // );
      // if (elevenlabsProvider) {
      //   const elevenlabsVoiceList =
      //     elevenlabsProvider.req_params_info.voice_list || [];
      //   const elevenlabsVoiceOptions: VoiceOption[] = elevenlabsVoiceList.map(
      //     (voice) => ({
      //       key: voice.voice,
      //       label: voice.name || voice.voice,
      //       value: voice.voice,
      //       originData: voice,
      //     })
      //   );

      //   // 更新 voices 中的 elevenlabs children
      //   const elevenlabsVoice = voices.find((v) => v.key === "elevenlabs");
      //   if (elevenlabsVoice) {
      //     elevenlabsVoice.children = elevenlabsVoiceOptions;
      //   }
      // }

      // 处理 openai 数据
      const openAiProvider = provider_list.find(
        (p) => p.provider.toLowerCase() === "openai"
      );
      if (openAiProvider) {
        const openAiVoiceList = openAiProvider.req_params_info.voice_list || [];
        // 只保留指定的几个音色
        const allowedVoices = [
          "alloy",
          "echo",
          "fable",
          "onyx",
          "nova",
          "shimmer",
        ];
        const filteredOpenAiVoiceList = openAiVoiceList.filter((voice) =>
          allowedVoices.includes(voice.voice.toLowerCase())
        );

        const openAiVoiceOptions = filteredOpenAiVoiceList.map((voice) => ({
          key: voice.voice,
          label: voice.gender
            ? `${voice.name.charAt(0).toUpperCase() + voice.name.slice(1)} ${
                voice.gender
                  ? `(${t(`common.${voice.gender.toLowerCase()}`)})`
                  : ""
              }`
            : voice.name.charAt(0).toUpperCase() + voice.name.slice(1),
          value: voice.voice,
          originData: voice,
        }));

        // 更新 voices 中的 openai children
        const openAiVoice = voices.find((v) => v.key === "OpenAI");
        if (openAiVoice) {
          openAiVoice.children = openAiVoiceOptions;
        }
      }

      // 如果需要持久化，可以更新到 store
      setVoiceStore((prev) => ({ ...prev, voiceList: voices }));
    })();
  }, [apiKey]);

  return (
    <div className="flex bg-background p-4">
      {/* 主容器 - 居中显示 */}
      <Card className="mx-auto w-full max-w-6xl p-6 shadow-sm">
        {/* 左右分栏布局 */}
        <div className="flex h-full flex-col gap-6 lg:flex-row">
          {/* 左侧区域 - 视频预览 (40%宽度) */}
          <div className="flex-1 lg:w-2/5">
            <VideoPreviewFrame
              coverImage={state.coverImageUrl}
              placeholder="视频封面预览"
            />
          </div>

          {/* 右侧区域 - 配置选项 (60%宽度) */}
          <div className="flex-1 lg:w-3/5">
            <ConfigurationPanel
              smallSelectValue={state.smallSelectValue}
              largeSelectValue={state.largeSelectValue}
              textContent={state.textContent}
              onSmallSelectChange={updateSmallSelect}
              onLargeSelectChange={updateLargeSelect}
              onTextChange={updateTextContent}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CreatePage;
