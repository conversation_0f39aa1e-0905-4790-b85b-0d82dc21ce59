# Design Document

## Overview

AI数字人创建页面是一个静态页面，采用现代化的响应式设计。页面使用Next.js 14、React 18、TypeScript和Tailwind CSS构建，集成了shadcn/ui组件库。页面布局采用左右分栏设计，左侧为视频预览区域，右侧为配置和输入区域。

## Architecture

### 技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: shadcn/ui (基于Radix UI)
- **国际化**: next-intl

### 页面结构
```
CreatePage
├── MainContainer (居中容器)
│   ├── LeftSection (视频预览区域)
│   │   └── VideoPreviewFrame (视频封面预览框)
│   └── RightSection (配置区域)
│       ├── TopControls (上方控制区域)
│       │   ├── SmallSelect (小下拉框)
│       │   └── LargeSelect (大下拉框)
│       └── BottomInput (下方输入区域)
│           └── TextArea (大文本输入框)
```

## Components and Interfaces

### 1. CreatePage 主组件
```typescript
interface CreatePageProps {
  // 无需props，作为页面组件
}
```

**职责:**
- 作为页面的根组件
- 管理整体布局和状态
- 协调子组件之间的交互

### 2. VideoPreviewFrame 组件
```typescript
interface VideoPreviewFrameProps {
  coverImage?: string;
  aspectRatio?: number;
  placeholder?: string;
}
```

**职责:**
- 显示视频封面图预览
- 处理无封面时的占位符显示
- 维持合适的宽高比

### 3. ConfigurationPanel 组件
```typescript
interface ConfigurationPanelProps {
  onSmallSelectChange: (value: string) => void;
  onLargeSelectChange: (value: string) => void;
  onTextChange: (value: string) => void;
}
```

**职责:**
- 管理右侧配置区域
- 处理用户输入和选择
- 向父组件传递状态变化

### 4. 使用的shadcn/ui组件
- **Card**: 用于主容器和预览框
- **Select**: 用于两个下拉框
- **Textarea**: 用于文本输入
- **Label**: 用于表单标签
- **AspectRatio**: 用于视频预览框的比例控制

## Data Models

### 1. 页面状态接口
```typescript
interface CreatePageState {
  // 小下拉框选择的值（如：语言选择）
  smallSelectValue: string;

  // 大下拉框选择的值（如：声音模型选择）
  largeSelectValue: string;

  // 文本输入内容
  textContent: string;

  // 视频封面图URL
  coverImageUrl?: string;
}
```

### 2. 下拉框选项接口
```typescript
interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectConfig {
  placeholder: string;
  options: SelectOption[];
}
```

## 布局设计

### 1. 主容器布局
- 使用Flexbox进行居中对齐
- 最大宽度限制，确保在大屏幕上不会过宽
- 响应式设计，在移动端切换为垂直布局

### 2. 左右分栏设计
- 左侧：占用40%宽度，用于视频预览
- 右侧：占用60%宽度，用于配置选项
- 在平板和手机端切换为上下布局

### 3. 视频预览区域
- 16:9的宽高比
- 圆角边框设计
- 支持拖拽上传（未来扩展）
- 占位符显示

### 4. 配置区域布局
- 上方：两个下拉框水平排列
  - 小下拉框：宽度约30%
  - 大下拉框：宽度约65%
  - 5%间距
- 下方：大文本输入框
  - 最小高度：120px
  - 支持自动调整高度

## 样式规范

### 1. 颜色方案
- 使用项目的CSS变量颜色系统
- 主要背景：`background`
- 卡片背景：`card`
- 边框颜色：`border`
- 文本颜色：`foreground`

### 2. 间距规范
- 主容器内边距：`p-6`
- 组件间距：`gap-6`
- 小组件间距：`gap-4`
- 表单元素间距：`space-y-4`

### 3. 圆角规范
- 主容器：`rounded-lg`
- 预览框：`rounded-md`
- 表单元素：使用shadcn/ui默认圆角

### 4. 阴影效果
- 主容器：`shadow-sm`
- 悬停效果：`hover:shadow-md`

## 响应式设计

### 1. 断点设计
- **Desktop (lg+)**: 左右分栏布局
- **Tablet (md-lg)**: 左右分栏，调整比例
- **Mobile (<md)**: 上下堆叠布局

### 2. 移动端适配
- 主容器在移动端使用全宽度
- 视频预览框在移动端保持16:9比例
- 下拉框在移动端堆叠显示
- 文本框在移动端调整高度

## Error Handling

### 1. 表单验证
- 文本输入长度限制
- 必填字段验证
- 实时验证反馈

### 2. 图片加载错误
- 封面图加载失败时显示占位符
- 提供重新上传选项

### 3. 用户体验优化
- 加载状态指示
- 操作反馈提示
- 防抖处理用户输入

## Testing Strategy

### 1. 单元测试
- 组件渲染测试
- 用户交互测试
- 状态管理测试

### 2. 集成测试
- 页面完整渲染测试
- 响应式布局测试
- 表单提交流程测试

### 3. 视觉回归测试
- 不同屏幕尺寸的截图对比
- 主题切换测试
- 浏览器兼容性测试

### 4. 可访问性测试
- 键盘导航测试
- 屏幕阅读器兼容性
- 颜色对比度检查

## 实现细节

### 1. 文件结构
```
src/app/[locale]/(main)/create/
├── page.tsx                 # 主页面组件
├── components/
│   ├── VideoPreviewFrame.tsx
│   ├── ConfigurationPanel.tsx
│   └── index.ts
├── hooks/
│   └── useCreatePage.ts     # 页面状态管理
└── types/
    └── index.ts             # 类型定义
```

### 2. 状态管理
- 使用React useState进行本地状态管理
- 自定义hook封装状态逻辑
- 未来可扩展为全局状态管理

### 3. 性能优化
- 使用React.memo优化组件重渲染
- 防抖处理文本输入
- 图片懒加载
- 代码分割和动态导入
