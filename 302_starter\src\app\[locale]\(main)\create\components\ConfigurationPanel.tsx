import React, { useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ConfigurationPanelProps } from "../types";
import { env } from "@/env";
import { appConfigAtom, store } from "@/stores";
import ky from "ky";
import { useTranslations } from "next-intl";
import { voices } from "@/constants/voices";
import { useAtom } from "jotai";
import { voiceStoreAtom } from "@/stores/slices/voice_store";

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  onSmallSelectChange,
  onLargeSelectChange,
  onTextChange,
  smallSelectValue,
  largeSelectValue,
  textContent,
}) => {
  const t = useTranslations();
  const [voiceStore] = useAtom(voiceStoreAtom);

  return (
    <div className="flex h-full flex-col gap-6">
      {/* 上方控制区域 - 两个下拉框 */}
      <div className="flex flex-col gap-4 sm:flex-row">
        {/* 小下拉框 (30%宽度) */}
        <div className="flex-none space-y-2 sm:w-[30%]">
          {/* <Label htmlFor="small-select" className="text-sm font-medium">
            语言
          </Label> */}
          <Select value={smallSelectValue} onValueChange={onSmallSelectChange}>
            <SelectTrigger id="small-select">
              <SelectValue placeholder="选择语言" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="zh">中文</SelectItem>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="ja">日本語</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 大下拉框 (65%宽度) */}
        <div className="flex-1 space-y-2 sm:w-[65%]">
          {/* <Label htmlFor="large-select" className="text-sm font-medium">
            声音模型
          </Label> */}
          <Select value={largeSelectValue} onValueChange={onLargeSelectChange}>
            <SelectTrigger id="large-select">
              <SelectValue placeholder="选择声音模型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="model1">标准女声</SelectItem>
              <SelectItem value="model2">标准男声</SelectItem>
              <SelectItem value="model3">甜美女声</SelectItem>
              <SelectItem value="model4">磁性男声</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 下方输入区域 - 大文本框 */}
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          {/* <Label htmlFor="text-content" className="text-sm font-medium">
            AI数字人说话内容
          </Label> */}
          {/* <span className="text-xs text-muted-foreground">
            {textContent.length} 字符
          </span> */}
        </div>
        <Textarea
          id="text-content"
          placeholder="请输入AI数字人需要说的内容..."
          value={textContent}
          onChange={(e) => onTextChange(e.target.value)}
          className="min-h-[120px] resize-y focus:border-transparent focus:ring-2 focus:ring-primary"
          rows={5}
        />
      </div>
    </div>
  );
};

export default ConfigurationPanel;
