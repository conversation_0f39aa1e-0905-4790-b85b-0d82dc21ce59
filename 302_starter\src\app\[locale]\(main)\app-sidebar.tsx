import type * as React from "react"
import { Bo<PERSON>, Plus, User, Mic, Folder<PERSON><PERSON> } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"

const navigationItems = [
  {
    title: "创建作品",
    icon: Plus,
    url: "#",
    isActive: true,
  },
  {
    title: "数字人像",
    icon: User,
    url: "#",
    isActive: false,
  },
  {
    title: "声音克隆",
    icon: Mic,
    url: "#",
    isActive: false,
  },
  {
    title: "作品管理",
    icon: FolderOpen,
    url: "#",
    isActive: false,
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar className="" {...props}>
      <SidebarHeader className="border-b p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" >
              <div className="flex items-center gap-3">
                <div className="flex aspect-square size-10 items-center justify-center rounded-lg  ">
                  <Bot className="size-6" />
                </div>
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="font-semibold  text-lg">标题</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="p-2">
        <SidebarMenu>
          {navigationItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                isActive={item.isActive}
                className={`
                  w-full justify-start gap-3 px-4 py-3 text-base font-medium

                `}
              >
                <a href={item.url} className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-5 h-5">

                  </div>
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
    </Sidebar>
  )
}
