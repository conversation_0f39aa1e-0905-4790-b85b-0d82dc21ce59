// 页面状态接口
export interface CreatePageState {
  // 小下拉框选择的值（如：语言选择）
  smallSelectValue: string;

  // 大下拉框选择的值（如：声音模型选择）
  largeSelectValue: string;

  // 文本输入内容
  textContent: string;

  // 视频封面图URL
  coverImageUrl?: string;
}

// 下拉框选项接口
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectConfig {
  placeholder: string;
  options: SelectOption[];
}

// VideoPreviewFrame 组件属性
export interface VideoPreviewFrameProps {
  coverImage?: string;
  aspectRatio?: number;
  placeholder?: string;
}

// ConfigurationPanel 组件属性
export interface ConfigurationPanelProps {
  onSmallSelectChange: (value: string) => void;
  onLargeSelectChange: (value: string) => void;
  onTextChange: (value: string) => void;
  smallSelectValue: string;
  largeSelectValue: string;
  textContent: string;
}
